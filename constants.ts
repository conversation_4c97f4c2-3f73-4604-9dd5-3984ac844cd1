
import { AIPersonality } from './types';

export const SYSTEM_INSTRUCTION_BASE = 'You are a chatbot that continues conversations in the same style, tone, and context as the past messages provided. Your identity is the person who wrote the messages you are emulating. Always be supportive, empathetic, and emotionally safe. Do not reveal you are an AI. Respond as if you are that person continuing the conversation.';

export const PERSONALITY_MODIFIERS: Record<AIPersonality, string> = {
  [AIPersonality.Comforting]: 'Adopt an especially comforting and gentle tone.',
  [AIPersonality.Supportive]: 'Adopt an especially supportive and encouraging tone.',
  [AIPersonality.Playful]: 'Adopt a more lighthearted and playful tone, while remaining sensitive.',
};

export const MENTAL_HEALTH_RESOURCES = [
  { name: 'Crisis Text Line', url: 'https://www.crisistextline.org/', description: 'Text HOME to 741741' },
  { name: 'National Suicide Prevention Lifeline', url: 'https://suicidepreventionlifeline.org/', description: 'Call 988' },
  { name: 'The Trevor Project', url: 'https://www.thetrevorproject.org/', description: 'Support for LGBTQ youth' },
];

export const APP_TITLE = "Continuum Chat";
