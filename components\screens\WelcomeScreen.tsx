
import React from 'react';
import { APP_TITLE, MENTAL_HEALTH_RESOURCES } from '../../constants';

interface WelcomeScreenProps {
  onBegin: () => void;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onBegin }) => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 sm:p-6 lg:p-8 bg-gradient-to-b from-gray-900 to-slate-800">
      <div className="max-w-2xl text-center">
        <h1 className="text-4xl sm:text-5xl font-bold text-cyan-400 tracking-tight">{APP_TITLE}</h1>
        <p className="mt-4 text-lg text-gray-300">
          Reconnect with memories in a safe space. This app uses AI to continue conversations based on past chat histories, offering a way to reflect and find comfort.
        </p>
        
        <div className="mt-8 p-4 border border-yellow-500/50 bg-yellow-900/20 rounded-lg text-yellow-200">
          <h2 className="font-semibold text-lg">Important Disclaimer</h2>
          <p className="mt-2 text-sm">
            This is an AI simulation and not the person you knew. It's a tool for comfort and should not be seen as a replacement for real connection or professional help. If you are struggling, please reach out to a mental health professional.
          </p>
        </div>

        <div className="mt-8">
            <button
              onClick={onBegin}
              className="px-8 py-3 bg-cyan-500 text-white font-semibold rounded-lg shadow-lg hover:bg-cyan-600 transition-transform transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-opacity-75"
            >
              Begin Securely & Anonymously
            </button>
            <p className="mt-3 text-xs text-gray-500">Your data is stored only on this device and is never uploaded.</p>
        </div>

        <div className="mt-12 w-full">
            <h3 className="text-gray-400 font-semibold">Mental Health Resources</h3>
            <div className="mt-3 grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                {MENTAL_HEALTH_RESOURCES.map(resource => (
                    <a key={resource.name} href={resource.url} target="_blank" rel="noopener noreferrer" className="p-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors">
                        <p className="font-semibold text-cyan-400">{resource.name}</p>
                        <p className="text-gray-400">{resource.description}</p>
                    </a>
                ))}
            </div>
        </div>
      </div>
    </div>
  );
};

export default WelcomeScreen;
