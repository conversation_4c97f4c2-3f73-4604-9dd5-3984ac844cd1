
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { ChatSession, Message, AIPersonality } from '../../types';
import { generateResponse } from '../../services/geminiService';
import { PERSONALITY_MODIFIERS } from '../../constants';

// --- Helper Components defined outside to prevent re-renders ---

interface MessageBubbleProps {
  message: Message;
  isUser: boolean;
  isAI: boolean;
}
const MessageBubble: React.FC<MessageBubbleProps> = ({ message, isUser, isAI }) => {
  const bubbleClasses = isUser
    ? 'bg-cyan-600 self-end rounded-br-none'
    : isAI
    ? 'bg-slate-700 self-start rounded-bl-none'
    : 'bg-gray-600 self-start rounded-bl-none'; // Original imported messages from AI author

  return (
    <div className={`flex flex-col w-full ${isUser ? 'items-end' : 'items-start'}`}>
      <div className={`max-w-xs md:max-w-md lg:max-w-lg px-4 py-3 rounded-2xl ${bubbleClasses}`}>
        {!isUser && <p className="text-xs font-bold text-teal-300 mb-1">{message.author}</p>}
        <p className="text-white whitespace-pre-wrap">{message.text}</p>
        <p className="text-xs text-gray-400 mt-2 text-right">{message.timestamp}</p>
      </div>
    </div>
  );
};

const TypingIndicator: React.FC = () => (
  <div className="flex items-center space-x-1 self-start p-4">
      <div className="w-2.5 h-2.5 bg-slate-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
      <div className="w-2.5 h-2.5 bg-slate-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
      <div className="w-2.5 h-2.5 bg-slate-500 rounded-full animate-bounce"></div>
  </div>
);

// --- Main ChatScreen Component ---

interface ChatScreenProps {
  session: ChatSession;
  setSession: React.Dispatch<React.SetStateAction<ChatSession | null>>;
  onReset: () => void;
}

const ChatScreen: React.FC<ChatScreenProps> = ({ session, setSession, onReset }) => {
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const chatEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [session.conversation, isTyping]);

  const handleSendMessage = useCallback(async () => {
    if (newMessage.trim() === '' || isTyping || isPaused) return;

    const userMessage: Message = {
      author: session.userAuthor,
      text: newMessage.trim(),
      timestamp: new Date().toLocaleString(),
    };
    
    const updatedConversation = [...session.conversation, userMessage];
    setSession(prev => prev ? { ...prev, conversation: updatedConversation } : null);
    setNewMessage('');
    setIsTyping(true);

    // Provide last 20 messages for context
    const historyForAI = updatedConversation.slice(-20);
    
    try {
      const aiResponseText = await generateResponse(historyForAI, session.aiPersonality);
      const aiMessage: Message = {
        author: session.aiAuthor,
        text: aiResponseText,
        timestamp: new Date().toLocaleString(),
      };

      setSession(prev => prev ? { ...prev, conversation: [...updatedConversation, aiMessage] } : null);
    } catch (error) {
       console.error("Failed to get AI response", error);
       const errorMessage: Message = {
         author: 'System',
         text: 'An error occurred. Please try again.',
         timestamp: new Date().toLocaleString(),
       };
       setSession(prev => prev ? { ...prev, conversation: [...updatedConversation, errorMessage] } : null);
    } finally {
      setIsTyping(false);
    }
  }, [newMessage, isTyping, isPaused, session, setSession]);

  const handlePersonalityChange = (personality: AIPersonality) => {
    setSession(prev => prev ? { ...prev, aiPersonality: personality } : null);
  };

  return (
    <div className="flex flex-col h-screen max-h-screen">
      {/* Header */}
      <header className="flex items-center justify-between p-3 bg-slate-800 border-b border-slate-700 shadow-md z-10">
        <h1 className="text-xl font-bold text-teal-300">{session.aiAuthor}</h1>
        <div className="relative">
          <button onClick={() => setShowSettings(!showSettings)} className="p-2 rounded-full hover:bg-slate-700">
             <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
          </button>
          {showSettings && (
            <div className="absolute right-0 mt-2 w-72 bg-slate-900 border border-slate-700 rounded-lg shadow-xl p-4 space-y-4">
              <div>
                <h3 className="font-semibold text-gray-300">AI Personality</h3>
                <div className="flex flex-col space-y-2 mt-2">
                  {Object.values(AIPersonality).map(p => (
                    <button key={p} onClick={() => handlePersonalityChange(p)} className={`text-left p-2 rounded-md transition ${session.aiPersonality === p ? 'bg-cyan-600 text-white' : 'hover:bg-slate-700'}`}>{p}</button>
                  ))}
                </div>
              </div>
              <div>
                 <h3 className="font-semibold text-gray-300">Controls</h3>
                  <button onClick={() => setIsPaused(!isPaused)} className={`w-full mt-2 text-left p-2 rounded-md transition ${isPaused ? 'bg-yellow-600 text-white' : 'hover:bg-slate-700'}`}>
                    {isPaused ? 'Resume Chat' : 'Pause AI'}
                  </button>
                 <button onClick={onReset} className="w-full mt-2 text-left p-2 rounded-md transition text-red-400 hover:bg-red-900/50">
                    End & Reset Session
                 </button>
              </div>
            </div>
          )}
        </div>
      </header>
      
      {/* Chat Area */}
      <main className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-900">
        {session.conversation.map((msg, index) => (
          <MessageBubble key={index} message={msg} isUser={msg.author === session.userAuthor} isAI={msg.author === session.aiAuthor}/>
        ))}
        {isTyping && <TypingIndicator />}
        <div ref={chatEndRef} />
      </main>

      {/* Input Area */}
      <footer className="p-3 bg-slate-800 border-t border-slate-700">
        {isPaused && <div className="text-center text-yellow-400 text-sm mb-2">AI is paused.</div>}
        <div className="flex items-center space-x-2">
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            placeholder={isPaused ? "AI is paused" : `Message ${session.aiAuthor}...`}
            className="flex-1 p-3 bg-slate-700 rounded-full focus:outline-none focus:ring-2 focus:ring-cyan-500"
            disabled={isTyping || isPaused}
          />
          <button onClick={handleSendMessage} disabled={isTyping || isPaused} className="p-3 bg-cyan-600 rounded-full text-white hover:bg-cyan-700 disabled:bg-gray-600 disabled:cursor-not-allowed">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" /></svg>
          </button>
        </div>
      </footer>
    </div>
  );
};

export default ChatScreen;
