
import React, { useState, useCallback } from 'react';
import { ChatSession, Message, AIPersonality } from './types';
import { useLocalStorage } from './hooks/useLocalStorage';
import WelcomeScreen from './components/screens/WelcomeScreen';
import ImportScreen from './components/screens/ImportScreen';
import ChatScreen from './components/screens/ChatScreen';

const App: React.FC = () => {
  const [session, setSession] = useLocalStorage<ChatSession | null>('chat-session', null);
  const [view, setView] = useState<'welcome' | 'import' | 'chat'>(session ? 'chat' : 'welcome');

  const handleBegin = () => {
    setView('import');
  };

  const handleImportComplete = (importedMessages: Message[], authors: string[], userAuthor: string, aiAuthor: string) => {
    const newSession: ChatSession = {
      originalMessages: importedMessages,
      conversation: importedMessages,
      authors,
      userAuthor,
      aiAuthor,
      aiPersonality: AIPersonality.Supportive,
    };
    setSession(newSession);
    setView('chat');
  };
  
  const handleReset = useCallback(() => {
    setSession(null);
    setView('welcome');
    window.localStorage.removeItem('chat-session');
  }, [setSession]);

  const renderContent = () => {
    if (view === 'chat' && session) {
      return <ChatScreen session={session} setSession={setSession} onReset={handleReset} />;
    }
    if (view === 'import') {
      return <ImportScreen onImportComplete={handleImportComplete} />;
    }
    return <WelcomeScreen onBegin={handleBegin} />;
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white flex flex-col">
      {renderContent()}
    </div>
  );
};

export default App;
