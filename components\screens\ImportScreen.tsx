
import React, { useState, useCallback } from 'react';
import { Message } from '../../types';
import { parseWhatsAppTxt } from '../../utils/chatParser';
import { APP_TITLE } from '../../constants';

interface ImportScreenProps {
  onImportComplete: (messages: Message[], authors: string[], userAuthor: string, aiAuthor: string) => void;
}

const ImportScreen: React.FC<ImportScreenProps> = ({ onImportComplete }) => {
  const [error, setError] = useState<string | null>(null);
  const [parsedData, setParsedData] = useState<{ messages: Message[], authors: string[] } | null>(null);
  const [userAuthor, setUserAuthor] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  const handleFileChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsLoading(true);
    setError(null);
    setParsedData(null);
    setUserAuthor(null);

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const result = parseWhatsAppTxt(content);

        if (result.messages.length === 0) {
          setError('Could not find any messages. Please check the file format. It should be a standard WhatsApp .txt export.');
        } else if (result.authors.length < 2) {
          setError(`Only found one participant. A conversation requires at least two.`);
        } else {
          setParsedData(result);
        }
      } catch (err) {
        setError('Failed to parse the file. Please ensure it is a valid text file.');
      } finally {
        setIsLoading(false);
      }
    };
    reader.onerror = () => {
        setError('Error reading file.');
        setIsLoading(false);
    };
    reader.readAsText(file);
  }, []);

  const handleAuthorSelection = (author: string) => {
    setUserAuthor(author);
  };

  const handleConfirm = () => {
    if (parsedData && userAuthor) {
      const aiAuthor = parsedData.authors.find(a => a !== userAuthor);
      if (aiAuthor) {
        onImportComplete(parsedData.messages, parsedData.authors, userAuthor, aiAuthor);
      } else {
        setError("Could not determine the other participant. Please re-upload.")
      }
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-gray-900">
      <div className="w-full max-w-lg p-8 space-y-6 bg-slate-800 rounded-xl shadow-2xl">
        <h1 className="text-2xl font-bold text-center text-cyan-400">{APP_TITLE}</h1>
        
        {!parsedData && (
          <div className="text-center">
            <h2 className="text-lg font-semibold">Import Your Chat History</h2>
            <p className="mt-2 text-sm text-gray-400">
              Upload a WhatsApp chat export (.txt file). Your data remains on your device.
            </p>
            <div className="mt-4">
              <label htmlFor="file-upload" className="cursor-pointer inline-flex items-center px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700 transition">
                {isLoading ? 'Processing...' : 'Select .txt File'}
              </label>
              <input id="file-upload" name="file-upload" type="file" accept=".txt" className="sr-only" onChange={handleFileChange} disabled={isLoading} />
            </div>
          </div>
        )}

        {error && <p className="text-center text-red-400 bg-red-900/50 p-3 rounded-md">{error}</p>}
        
        {parsedData && !userAuthor && (
          <div className="text-center">
            <h3 className="font-semibold text-lg">Who are you in this conversation?</h3>
            <p className="text-sm text-gray-400 mb-4">Select your name to continue.</p>
            <div className="flex flex-col space-y-2">
              {parsedData.authors.map(author => (
                <button
                  key={author}
                  onClick={() => handleAuthorSelection(author)}
                  className="w-full px-4 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition"
                >
                  {author}
                </button>
              ))}
            </div>
          </div>
        )}

        {parsedData && userAuthor && (
          <div className="text-center">
            <p className="text-lg">Ready to begin your conversation.</p>
            <p className="text-sm text-gray-400 mt-2">
              You are: <span className="font-bold text-cyan-400">{userAuthor}</span><br/>
              Your companion is: <span className="font-bold text-teal-400">{parsedData.authors.find(a => a !== userAuthor)}</span>
            </p>
            <button
              onClick={handleConfirm}
              className="mt-6 w-full px-4 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 transition"
            >
              Start Chatting
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImportScreen;

