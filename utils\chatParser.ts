
import { Message } from '../types';

// Regex to match a WhatsApp line: [DD/MM/YYYY, HH:MM:SS] Author: Message
const WHATSAPP_LINE_REGEX = /^\[(\d{1,2}\/\d{1,2}\/\d{4}),\s(\d{1,2}:\d{2}:\d{2})\]\s([^:]+):\s(.*)$/;

export const parseWhatsAppTxt = (txt: string): { messages: Message[], authors: string[] } => {
  const lines = txt.split('\n');
  const messages: Message[] = [];
  const authorSet = new Set<string>();

  lines.forEach(line => {
    const match = line.match(WHATSAPP_LINE_REGEX);
    if (match) {
      const [_, date, time, author, text] = match;
      const timestamp = `${date} ${time}`;
      messages.push({ author: author.trim(), text: text.trim(), timestamp });
      authorSet.add(author.trim());
    } else if (messages.length > 0) {
      // This is a multi-line message, append it to the last message.
      messages[messages.length - 1].text += `\n${line.trim()}`;
    }
  });
  
  return { messages, authors: Array.from(authorSet) };
};
