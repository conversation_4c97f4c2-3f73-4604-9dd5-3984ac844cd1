import { GoogleGenA<PERSON> } from "@google/genai";
import { Message } from '../types';
import { SYSTEM_INSTRUCTION_BASE, PERSONALITY_MODIFIERS } from '../constants';
import { AIPersonality } from '../types';

if (!process.env.API_KEY) {
  // In a real app, this would be handled by the build environment.
  // For this context, we'll alert the user if it's missing.
  // This check is primarily for development and to prevent runtime errors.
  console.warn("API_KEY environment variable not set. Using a placeholder. AI features will not work.");
}

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY || " " });

export const generateResponse = async (
  history: Message[],
  personality: AIPersonality
): Promise<string> => {
  if (!process.env.API_KEY) {
    return Promise.resolve("AI is offline. This is a simulated response.");
  }

  const systemInstruction = `${SYSTEM_INSTRUCTION_BASE} ${PERSONALITY_MODIFIERS[personality]}`;

  try {
    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      // FIX: The `contents` property must be an array of Content objects.
      contents: [{
        role: 'user',
        parts: [{ text: `Based on the following conversation, continue it:\n\n---\n${history.map(m => `${m.author}: ${m.text}`).join('\n')}\n---` }]
      }],
      config: {
        systemInstruction,
        // The thinking config is omitted to use the default (higher quality) setting.
      }
    });
    
    // Using response.text as per guidelines
    return response.text;
  } catch (error) {
    console.error("Error generating response from Gemini API:", error);
    return "I'm having trouble connecting right now. Please try again in a moment.";
  }
};